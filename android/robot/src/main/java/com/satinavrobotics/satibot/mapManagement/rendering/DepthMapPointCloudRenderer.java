package com.satinavrobotics.satibot.mapManagement.rendering;

import android.content.Context;
import android.opengl.GLES20;
import android.util.Log;

import com.google.ar.core.Frame;
import com.google.ar.core.Camera;
import com.google.ar.core.CameraIntrinsics;
import com.google.ar.core.Pose;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.FloatBuffer;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import com.google.ar.core.exceptions.NotYetAvailableException;
import com.satinavrobotics.satibot.mapManagement.pcd_processing.AsyncVoxelFilter;
import com.satinavrobotics.satibot.mapManagement.PointCloudGenerator;

/**
 * Renders a point cloud generated by PointCloudGenerator from ARCore depth data.
 * The point cloud is rendered with OpenGL ES 2.0.
 */
public class DepthMapPointCloudRenderer {
    private static final String TAG = DepthMapPointCloudRenderer.class.getSimpleName();
    private static final int MAX_POINTS = 1000000;
    private static final int FLOATS_PER_POINT = 7; // x, y, z, r, g, b, confidence
    private static final float POINT_SIZE = 10.0f; // Size of points in the point cloud
    private static final int FRAME_SKIP_COUNT = 5; // Process every Nth frame
    private static final float VOXEL_SIZE = 0.1f; // Voxel size in meters for subsampling (2cm)

    // Shader names
    private static final String VERTEX_SHADER_NAME = "shaders/colored_point_cloud.vert";
    private static final String FRAGMENT_SHADER_NAME = "shaders/colored_point_cloud.frag";

    private final FloatBuffer pointBuffer;
    private int numPoints = 0;
    private int program = -1;
    private int positionAttr = -1;
    private int colorAttr = -1;
    private int confidenceAttr = -1;
    private int mvpMatrixUniform = -1;
    private int pointSizeUniform = -1;
    private float confidenceThreshold = 0.5f;

    // Thread-safe point cloud storage with synchronization
    private final ReentrantReadWriteLock pointCloudLock = new ReentrantReadWriteLock();
    private final List<float[]> accumulatedPoints = Collections.synchronizedList(new ArrayList<>());

    private final float[] modelMatrix = new float[16]; // Model matrix for transforming points
    private final float[] tempPoint = new float[4]; // Temporary point for transformations
    private Context context;

    // Frame processing variables
    private int frameCounter = 0;
    private boolean voxelFilterEnabled = true;
    private boolean outlierDetectionEnabled = true;
    private boolean medianFilterEnabled = true;

    // Asynchronous filtering
    private AsyncVoxelFilter asyncVoxelFilter;
    private boolean asyncFilteringInProgress = false;

    public DepthMapPointCloudRenderer() {
        pointBuffer = ByteBuffer.allocateDirect(MAX_POINTS * FLOATS_PER_POINT * Float.BYTES)
                .order(ByteOrder.nativeOrder())
                .asFloatBuffer();

        // Initialize the asynchronous voxel filter
        asyncVoxelFilter = new AsyncVoxelFilter();

        // Initialize the outlier detector
        PointCloudGenerator.initOutlierDetector();

        // Initialize median filtering
        PointCloudGenerator.setMedianFilterEnabled(medianFilterEnabled);
    }

    public void createOnGlThread(Context context) throws IOException {
        this.context = context;
        try {
            initGL();
        } catch (Exception e) {
            Log.e(TAG, "Error in createOnGlThread: " + e.getMessage());
            throw new IOException("Failed to initialize OpenGL resources", e);
        }
    }

    public void setConfidenceThreshold(int progress) {
        this.confidenceThreshold = progress / 255.0f;
    }

    public void resetAccumulatedPointCloud() {
        pointCloudLock.writeLock().lock();
        try {
            accumulatedPoints.clear();
        } finally {
            pointCloudLock.writeLock().unlock();
        }
    }

    /**
     * Enables or disables voxel grid filtering of the accumulated point cloud.
     * @param enabled Whether voxel filtering should be applied
     */
    public void setVoxelFilterEnabled(boolean enabled) {
        this.voxelFilterEnabled = enabled;
    }

    /**
     * Enables or disables outlier detection for each frame.
     * @param enabled Whether outlier detection should be applied
     */
    public void setOutlierDetectionEnabled(boolean enabled) {
        this.outlierDetectionEnabled = enabled;
        PointCloudGenerator.setOutlierDetectionEnabled(enabled);
    }

    /**
     * Enables or disables median filtering of depth images.
     * @param enabled Whether median filtering should be applied
     */
    public void setMedianFilterEnabled(boolean enabled) {
        this.medianFilterEnabled = enabled;
        PointCloudGenerator.setMedianFilterEnabled(enabled);
    }

    /**
     * Applies voxel grid filtering to the accumulated point cloud.
     * This reduces the number of points while maintaining the overall structure.
     * The filtering is performed asynchronously on a background thread.
     */
    public void applyVoxelFilter() {
        // Skip if already processing or if there are no points
        if (asyncFilteringInProgress || accumulatedPoints.isEmpty()) {
            return;
        }

        // Mark as processing
        asyncFilteringInProgress = true;

        // Create a copy of the accumulated points with read lock
        final List<float[]> pointsToFilter = new ArrayList<>();
        pointCloudLock.readLock().lock();
        try {
            pointsToFilter.addAll(accumulatedPoints);
        } finally {
            pointCloudLock.readLock().unlock();
        }

        // Process asynchronously
        asyncVoxelFilter.filterAsync(pointsToFilter, VOXEL_SIZE, filteredPoints -> {
            // Update the accumulated points with write lock
            pointCloudLock.writeLock().lock();
            try {
                accumulatedPoints.clear();
                accumulatedPoints.addAll(filteredPoints);
                Log.d(TAG, "Applied async voxel grid filter: reduced to " + filteredPoints.size() + " points");
            } finally {
                pointCloudLock.writeLock().unlock();
                asyncFilteringInProgress = false;
            }
        });
    }

    /**
     * Updates the point cloud from the given depth, confidence, and YUV color buffers.
     * @param depthArray short[] depth map (millimeters)
     * @param confidenceArray byte[] confidence map (0-255)
     * @param width depth width
     * @param height depth height
     * @param yuvY byte[] Y plane
     * @param yuvU byte[] U plane
     * @param yuvV byte[] V plane
     * @param yRowStride int
     * @param uvRowStride int
     * @param uvPixelStride int
     * @param colorWidth int
     * @param colorHeight int
     * @param intrinsics CameraIntrinsics
     * @param confidenceThreshold float (0-1)
     */
    public void update(short[] depthArray, byte[] confidenceArray, int width, int height,
                      byte[] yuvY, byte[] yuvU, byte[] yuvV, int yRowStride, int uvRowStride, int uvPixelStride,
                      int colorWidth, int colorHeight, CameraIntrinsics intrinsics, float confidenceThreshold) {
        pointBuffer.rewind();
        numPoints = 0;
        boolean noConfidence = (confidenceArray == null || confidenceArray.length != depthArray.length);
        int imageWidth = intrinsics.getImageDimensions()[0];
        int imageHeight = intrinsics.getImageDimensions()[1];
        float fx = intrinsics.getFocalLength()[0] * width / imageWidth;
        float fy = intrinsics.getFocalLength()[1] * height / imageHeight;
        float cx = intrinsics.getPrincipalPoint()[0] * width / imageWidth;
        float cy = intrinsics.getPrincipalPoint()[1] * height / imageHeight;
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int idx = y * width + x;
                float depthM = depthArray[idx] * 0.001f;
                float confidence = noConfidence ? 1f : (confidenceArray[idx] & 0xFF) / 255f;
                if (depthM == 0f || confidence < confidenceThreshold) continue;


                // Project to camera space
                float X = (x - cx) * depthM / fx;
                float Y = (cy - y) * depthM / fy; // Flipped Y-axis
                float Z = depthM;
                // Map to color image
                int colorX = x * colorWidth / width;
                int colorY = y * colorHeight / height;
                int yIdx = colorY * yRowStride + colorX;
                int uvIdx = (colorY / 2) * uvRowStride + (colorX / 2) * uvPixelStride;
                byte yVal = yuvY[yIdx];
                byte uVal = yuvU[uvIdx];
                byte vVal = yuvV[uvIdx];
                float[] rgb = yuvToRgb(yVal, uVal, vVal);
                // Store point
                pointBuffer.put(X);
                pointBuffer.put(Y);
                pointBuffer.put(Z);
                pointBuffer.put(rgb[0]);
                pointBuffer.put(rgb[1]);
                pointBuffer.put(rgb[2]);
                pointBuffer.put(confidence);
                numPoints++;
                if (numPoints >= MAX_POINTS) break;
            }
            if (numPoints >= MAX_POINTS) break;
        }
        pointBuffer.rewind();
    }

    public void update(Frame frame) {
        try {
            // Increment frame counter
            frameCounter++;

            // Only process every Nth frame to reduce computational load
            if (frameCounter % FRAME_SKIP_COUNT != 0) {
                return;
            }

            // Use a subsample factor to reduce the number of points
            int subsampleFactor = 2;

            // Generate colored point cloud with confidence threshold and timestamp checking
            List<float[]> generatedPoints = PointCloudGenerator.generateColoredPointCloud(
                    frame, confidenceThreshold, subsampleFactor, true, true);

            if (generatedPoints.isEmpty()) {
                return; // No points generated or timestamps didn't match
            }

            // Get the camera pose for transforming points to world space
            Camera camera = frame.getCamera();
            Pose cameraPose = camera.getPose();
            cameraPose.toMatrix(modelMatrix, 0);

            // Apply outlier detection to the generated points if enabled
            if (outlierDetectionEnabled) {
                // Process outliers asynchronously
                PointCloudGenerator.detectOutliersAsync(generatedPoints, filteredPoints -> {
                    // Process the filtered points
                    processFilteredPoints(filteredPoints, cameraPose);
                });
            } else {
                // Process points directly without outlier detection
                processFilteredPoints(generatedPoints, cameraPose);
            }

        } catch (NotYetAvailableException e) {
            Log.e(TAG, "Depth not yet available: " + e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "Error in update: " + e.getMessage());
        }
    }

    /**
     * Processes filtered points after outlier detection.
     *
     * @param filteredPoints Points after outlier detection
     * @param cameraPose Camera pose for the current frame
     */
    private void processFilteredPoints(List<float[]> filteredPoints, Pose cameraPose) {
        // Clear the point buffer and prepare for new points
        pointBuffer.rewind();
        numPoints = 0;

        // Process each point from the filtered point cloud
        for (float[] point : filteredPoints) {
            if (numPoints >= MAX_POINTS) break;

            // Each point now contains position, color, and confidence
            // Format: [x, y, z, r, g, b, confidence]
            if (point.length >= 7) {
                // Store position (already in world space from PointCloudGenerator)
                pointBuffer.put(point[0]); // x
                pointBuffer.put(point[1]); // y
                pointBuffer.put(point[2]); // z

                // Store color and confidence
                pointBuffer.put(point[3]); // r
                pointBuffer.put(point[4]); // g
                pointBuffer.put(point[5]); // b
                pointBuffer.put(point[6]); // confidence

                numPoints++;
            }
        }

        pointBuffer.rewind();

        // Add points to accumulated list
        accumulatePoints(cameraPose);

        // Apply voxel grid filtering to the accumulated point cloud
        if (voxelFilterEnabled) {
            applyVoxelFilter();
        }

        Log.d(TAG, "Updated point cloud with " + numPoints + " colored points (frame " +
              frameCounter + ", confidence threshold: " + confidenceThreshold +
              ", median filter: " + medianFilterEnabled +
              ", outlier detection: " + outlierDetectionEnabled + ")");
    }

    private void accumulatePoints(Pose cameraPose) {
        // Prepare points to add
        List<float[]> newPoints = new ArrayList<>(numPoints);

        pointBuffer.rewind();
        for (int i = 0; i < numPoints; i++) {
            float[] pt = new float[FLOATS_PER_POINT];

            // Get position - points are already in world space from PointCloudGenerator
            pt[0] = pointBuffer.get(); // x
            pt[1] = pointBuffer.get(); // y
            pt[2] = pointBuffer.get(); // z

            // Get color and confidence
            pt[3] = pointBuffer.get(); // r
            pt[4] = pointBuffer.get(); // g
            pt[5] = pointBuffer.get(); // b
            pt[6] = pointBuffer.get(); // confidence

            newPoints.add(pt);
        }
        pointBuffer.rewind();

        // Add new points with write lock
        pointCloudLock.writeLock().lock();
        try {
            accumulatedPoints.addAll(newPoints);
            Log.d(TAG, "Added " + newPoints.size() + " points to accumulated point cloud (total: " +
                  accumulatedPoints.size() + ")");
        } finally {
            pointCloudLock.writeLock().unlock();
        }
    }

    /**
     * Renders the point cloud. Assumes an OpenGL context is current.
     * @param mvpMatrix 4x4 model-view-projection matrix
     */
    public void draw(float[] mvpMatrix) {
        if (program == -1) {
            try {
                initGL();
                if (program == -1) {
                    Log.e(TAG, "Failed to initialize GL program");
                    return;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error initializing GL in draw: " + e.getMessage());
                return;
            }
        }

        if (numPoints <= 0) {
            return; // Nothing to draw
        }

        try {
            GLES20.glUseProgram(program);
            GLES20.glUniformMatrix4fv(mvpMatrixUniform, 1, false, mvpMatrix, 0);
            GLES20.glUniform1f(pointSizeUniform, POINT_SIZE);

            pointBuffer.position(0);
            GLES20.glEnableVertexAttribArray(positionAttr);
            GLES20.glVertexAttribPointer(positionAttr, 3, GLES20.GL_FLOAT, false, FLOATS_PER_POINT * Float.BYTES, pointBuffer);

            pointBuffer.position(3);
            GLES20.glEnableVertexAttribArray(colorAttr);
            GLES20.glVertexAttribPointer(colorAttr, 3, GLES20.GL_FLOAT, false, FLOATS_PER_POINT * Float.BYTES, pointBuffer);

            pointBuffer.position(6);
            GLES20.glEnableVertexAttribArray(confidenceAttr);
            GLES20.glVertexAttribPointer(confidenceAttr, 1, GLES20.GL_FLOAT, false, FLOATS_PER_POINT * Float.BYTES, pointBuffer);

            GLES20.glDrawArrays(GLES20.GL_POINTS, 0, numPoints);

            GLES20.glDisableVertexAttribArray(positionAttr);
            GLES20.glDisableVertexAttribArray(colorAttr);
            GLES20.glDisableVertexAttribArray(confidenceAttr);
        } catch (Exception e) {
            Log.e(TAG, "Error in draw: " + e.getMessage());
        }
    }

    public void drawAccumulated(float[] viewMatrix, float[] projectionMatrix) {
        if (program == -1) {
            try {
                initGL();
                if (program == -1) {
                    Log.e(TAG, "Failed to initialize GL program in drawAccumulated");
                    return;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error initializing GL in drawAccumulated: " + e.getMessage());
                return;
            }
        }

        // Create a copy of the accumulated points with read lock
        List<float[]> pointsToDraw = new ArrayList<>();
        pointCloudLock.readLock().lock();
        try {
            if (accumulatedPoints.isEmpty()) {
                pointCloudLock.readLock().unlock();
                return; // Nothing to draw
            }
            pointsToDraw.addAll(accumulatedPoints);
        } finally {
            pointCloudLock.readLock().unlock();
        }

        try {
            float[] mvpMatrix = new float[16];
            android.opengl.Matrix.multiplyMM(mvpMatrix, 0, projectionMatrix, 0, viewMatrix, 0);

            GLES20.glUseProgram(program);
            GLES20.glUniformMatrix4fv(mvpMatrixUniform, 1, false, mvpMatrix, 0);
            GLES20.glUniform1f(pointSizeUniform, POINT_SIZE);

            FloatBuffer accBuffer = ByteBuffer.allocateDirect(pointsToDraw.size() * FLOATS_PER_POINT * Float.BYTES)
                    .order(ByteOrder.nativeOrder()).asFloatBuffer();
            for (float[] pt : pointsToDraw) accBuffer.put(pt);
            accBuffer.rewind();

            accBuffer.position(0);
            GLES20.glEnableVertexAttribArray(positionAttr);
            GLES20.glVertexAttribPointer(positionAttr, 3, GLES20.GL_FLOAT, false, FLOATS_PER_POINT * Float.BYTES, accBuffer);

            accBuffer.position(3);
            GLES20.glEnableVertexAttribArray(colorAttr);
            GLES20.glVertexAttribPointer(colorAttr, 3, GLES20.GL_FLOAT, false, FLOATS_PER_POINT * Float.BYTES, accBuffer);

            accBuffer.position(6);
            GLES20.glEnableVertexAttribArray(confidenceAttr);
            GLES20.glVertexAttribPointer(confidenceAttr, 1, GLES20.GL_FLOAT, false, FLOATS_PER_POINT * Float.BYTES, accBuffer);

            GLES20.glDrawArrays(GLES20.GL_POINTS, 0, pointsToDraw.size());

            GLES20.glDisableVertexAttribArray(positionAttr);
            GLES20.glDisableVertexAttribArray(colorAttr);
            GLES20.glDisableVertexAttribArray(confidenceAttr);
        } catch (Exception e) {
            Log.e(TAG, "Error in drawAccumulated: " + e.getMessage());
        }
    }

    private void initGL() {
        // Only initialize once
        if (program != -1) {
            return;
        }

        try {
            // Define simple inline shaders instead of loading from files
            // This avoids potential file loading issues
            String vertexShaderCode =
                    "uniform mat4 u_ModelViewProjection;\n" +
                    "uniform float u_PointSize;\n" +
                    "attribute vec3 a_Position;\n" +
                    "attribute vec3 a_Color;\n" +
                    "attribute float a_Confidence;\n" +
                    "varying vec3 v_Color;\n" +
                    "varying float v_Confidence;\n" +
                    "void main() {\n" +
                    "   v_Color = a_Color;\n" +
                    "   v_Confidence = a_Confidence;\n" +
                    "   gl_Position = u_ModelViewProjection * vec4(a_Position, 1.0);\n" +
                    "   gl_PointSize = u_PointSize;\n" +
                    "}\n";

            String fragmentShaderCode =
                    "precision mediump float;\n" +
                    "varying vec3 v_Color;\n" +
                    "varying float v_Confidence;\n" +
                    "void main() {\n" +
                    "    gl_FragColor = vec4(v_Color, v_Confidence);\n" +
                    "    vec2 center = vec2(0.5, 0.5);\n" +
                    "    float dist = distance(gl_PointCoord, center);\n" +
                    "    if (dist > 0.5) {\n" +
                    "        discard;\n" +
                    "    }\n" +
                    "}\n";

            // Compile vertex shader
            int vertexShader = GLES20.glCreateShader(GLES20.GL_VERTEX_SHADER);
            GLES20.glShaderSource(vertexShader, vertexShaderCode);
            GLES20.glCompileShader(vertexShader);

            // Check vertex shader compilation
            int[] compileStatus = new int[1];
            GLES20.glGetShaderiv(vertexShader, GLES20.GL_COMPILE_STATUS, compileStatus, 0);
            if (compileStatus[0] == 0) {
                Log.e(TAG, "Error compiling vertex shader: " + GLES20.glGetShaderInfoLog(vertexShader));
                GLES20.glDeleteShader(vertexShader);
                return;
            }

            // Compile fragment shader
            int fragmentShader = GLES20.glCreateShader(GLES20.GL_FRAGMENT_SHADER);
            GLES20.glShaderSource(fragmentShader, fragmentShaderCode);
            GLES20.glCompileShader(fragmentShader);

            // Check fragment shader compilation
            GLES20.glGetShaderiv(fragmentShader, GLES20.GL_COMPILE_STATUS, compileStatus, 0);
            if (compileStatus[0] == 0) {
                Log.e(TAG, "Error compiling fragment shader: " + GLES20.glGetShaderInfoLog(fragmentShader));
                GLES20.glDeleteShader(vertexShader);
                GLES20.glDeleteShader(fragmentShader);
                return;
            }

            // Create and link shader program
            program = GLES20.glCreateProgram();
            GLES20.glAttachShader(program, vertexShader);
            GLES20.glAttachShader(program, fragmentShader);
            GLES20.glLinkProgram(program);

            // Check program linking
            int[] linkStatus = new int[1];
            GLES20.glGetProgramiv(program, GLES20.GL_LINK_STATUS, linkStatus, 0);
            if (linkStatus[0] == 0) {
                Log.e(TAG, "Error linking program: " + GLES20.glGetProgramInfoLog(program));
                GLES20.glDeleteProgram(program);
                GLES20.glDeleteShader(vertexShader);
                GLES20.glDeleteShader(fragmentShader);
                program = -1;
                return;
            }

            // Get attribute locations
            positionAttr = GLES20.glGetAttribLocation(program, "a_Position");
            colorAttr = GLES20.glGetAttribLocation(program, "a_Color");
            confidenceAttr = GLES20.glGetAttribLocation(program, "a_Confidence");

            // Get uniform locations
            mvpMatrixUniform = GLES20.glGetUniformLocation(program, "u_ModelViewProjection");
            pointSizeUniform = GLES20.glGetUniformLocation(program, "u_PointSize");

            // Clean up shader objects - they're linked to the program now
            GLES20.glDeleteShader(vertexShader);
            GLES20.glDeleteShader(fragmentShader);

            Log.d(TAG, "Successfully initialized OpenGL shaders and program");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing shaders: " + e.getMessage());
            program = -1;
        }
    }

    // YUV420 to RGB conversion (range 0..1)
    private float[] yuvToRgb(byte y, byte u, byte v) {
        float yf = (y & 0xFF) / 255.0f;
        float uf = ((u & 0xFF) - 128) / 255.0f;
        float vf = ((v & 0xFF) - 128) / 255.0f;
        float r = yf + 1.402f * vf;
        float g = yf - 0.344136f * uf - 0.714136f * vf;
        float b = yf + 1.772f * uf;
        return new float[]{clamp(r), clamp(g), clamp(b)};
    }
    private float clamp(float v) { return Math.max(0f, Math.min(1f, v)); }

    /**
     * Cleans up resources used by the renderer.
     * Call this method when the renderer is no longer needed.
     */
    public void cleanup() {
        // Shut down the async voxel filter
        if (asyncVoxelFilter != null) {
            asyncVoxelFilter.shutdown();
            asyncVoxelFilter = null;
        }

        // Shut down the outlier detector
        PointCloudGenerator.shutdownOutlierDetector();

        // Clear accumulated points
        resetAccumulatedPointCloud();
    }
}
