package com.satinavrobotics.satibot.navigation;

import com.satinavrobotics.satibot.vehicle.pd.ControllerParameters;

import timber.log.Timber;

/**
 * PD (Proportional-Derivative) waypoint controller implementation.
 * Uses PD control for both turning and course correction.
 */
public class PDWaypointController implements WaypointController {
    private static final String TAG = PDWaypointController.class.getSimpleName();
    
    private final ControllerParameters parameters;
    
    // State for derivative calculations
    private float lastTurningError = 0.0f;
    private float lastCorrectionError = 0.0f;
    private boolean firstTurningUpdate = true;
    private boolean firstCorrectionUpdate = true;
    
    public PDWaypointController(ControllerParameters parameters) {
        this.parameters = parameters;
        Timber.d("Initialized PDWaypointController with parameters: %s", parameters);
    }
    
    @Override
    public float calculateTurningAngularVelocity(float headingError, float maxTurnSpeed, float deltaTime) {
        // Calculate derivative term
        float derivative = 0.0f;
        if (!firstTurningUpdate && deltaTime > 0.0f) {
            derivative = (headingError - lastTurningError) / deltaTime;
        }
        
        // PD control calculation
        float proportional = parameters.turningKp * headingError;
        float derivativeTerm = parameters.turningKd * derivative;
        float controlOutput = proportional + derivativeTerm;
        
        // Apply speed limits and ensure we don't exceed maxTurnSpeed
        float angularVelocity = Math.max(-maxTurnSpeed, Math.min(maxTurnSpeed, controlOutput));
        
        // Update state for next iteration
        lastTurningError = headingError;
        firstTurningUpdate = false;
        
        float headingErrorDegrees = (float) Math.toDegrees(Math.abs(headingError));
        Timber.d("PD turning: error=%.1f°, P=%.3f, D=%.3f, output=%.3f, angular=%.3f", 
                headingErrorDegrees, proportional, derivativeTerm, controlOutput, angularVelocity);
        
        return angularVelocity;
    }
    
    @Override
    public float calculateCourseCorrection(float headingError, float maxCorrectionStrength, float thresholdDegrees, float deltaTime) {
        float headingErrorDegrees = (float) Math.toDegrees(Math.abs(headingError));
        
        // Apply threshold check - only apply correction if error is significant enough
        if (headingErrorDegrees <= thresholdDegrees) {
            return 0.0f;
        }
        
        // Calculate derivative term
        float derivative = 0.0f;
        if (!firstCorrectionUpdate && deltaTime > 0.0f) {
            derivative = (headingError - lastCorrectionError) / deltaTime;
        }
        
        // PD control calculation
        float proportional = parameters.correctionKp * headingError;
        float derivativeTerm = parameters.correctionKd * derivative;
        float controlOutput = proportional + derivativeTerm;
        
        // Apply correction strength limits
        float angularVelocity = Math.max(-maxCorrectionStrength, Math.min(maxCorrectionStrength, controlOutput));
        
        // Update state for next iteration
        lastCorrectionError = headingError;
        firstCorrectionUpdate = false;
        
        Timber.d("PD correction: error=%.1f°, P=%.3f, D=%.3f, output=%.3f, angular=%.3f", 
                headingErrorDegrees, proportional, derivativeTerm, controlOutput, angularVelocity);
        
        return angularVelocity;
    }
    
    @Override
    public void reset() {
        // Clear derivative state
        lastTurningError = 0.0f;
        lastCorrectionError = 0.0f;
        firstTurningUpdate = true;
        firstCorrectionUpdate = true;
        
        Timber.d("PD controller reset - cleared derivative state");
    }
    
    @Override
    public String getControllerName() {
        return "PD Controller";
    }
}
