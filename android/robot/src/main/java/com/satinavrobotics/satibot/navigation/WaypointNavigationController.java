package com.satinavrobotics.satibot.navigation;

import android.os.Handler;
import android.os.Looper;

import com.google.ar.core.Pose;
import com.satinavrobotics.satibot.vehicle.pd.ControllerParameters;
import com.satinavrobotics.satibot.vehicle.Vehicle;

import org.json.JSONException;
import org.json.JSONObject;

import timber.log.Timber;

/**
 * Controller for autonomous navigation between waypoints using depth-based obstacle avoidance.
 *
 * This class manages sequential waypoint navigation by:
 * 1. Turning in place towards the target waypoint until rotation threshold is met
 * 2. Moving straight towards the waypoint until position threshold is met
 * 3. Using navigation map data to determine speed/stop when moving straight
 * 4. No traversability checking when turning in place
 *
 * The controller operates in two main states:
 * - TURNING: Rotating towards the waypoint
 * - MOVING: Moving straight towards the waypoint
 *
 * When all waypoints are reached, the controller stops.
 */
public class WaypointNavigationController {
    private static final String TAG = WaypointNavigationController.class.getSimpleName();

    // Navigation states
    public enum NavigationState {
        IDLE,           // Not navigating
        TURNING,        // Turning towards waypoint
        MOVING,         // Moving towards waypoint
        COMPLETED       // All waypoints reached
    }

    // Navigation parameters
    private static final float ROTATION_THRESHOLD_DEGREES = 22.5f;  // Degrees tolerance for rotation
    private static final float POSITION_THRESHOLD_METERS = 0.2f;    // Meters tolerance for position
    private static final float MAX_TURN_SPEED = 0.75f;              // Maximum angular velocity when turning
    private static final float MAX_MOVE_SPEED = 0.25f;              // Maximum linear velocity when moving
    private static final float MIN_MOVE_SPEED = 0.15f;              // Minimum linear velocity when moving

    // Core components
    private final Vehicle vehicle;
    private final WaypointsManager waypointsManager;
    private final NavigationController navigationController;
    private final Handler mainHandler;
    private WaypointController waypointController;

    // Navigation state
    private NavigationState currentState = NavigationState.IDLE;
    private JSONObject currentWaypoint = null;
    private Pose currentPose = null;
    private int currentWaypointIndex = 0;
    private int totalWaypointCount = 0;

    // Timing for controller deltaTime calculations
    private long lastUpdateTime = 0;

    // Navigation listener interface
    public interface NavigationListener {
        /**
         * Called when navigation state changes
         * @param state The new navigation state
         * @param waypoint Current waypoint being navigated to (null if none)
         */
        void onNavigationStateChanged(NavigationState state, JSONObject waypoint);

        /**
         * Called when navigation is completed (all waypoints reached)
         */
        void onNavigationCompleted();

        /**
         * Called when navigation encounters an error
         * @param error Error message
         */
        void onNavigationError(String error);
    }

    private NavigationListener navigationListener;

    /**
     * Constructor for WaypointNavigationController
     *
     * @param vehicle The vehicle to control
     * @param waypointsManager Manager for waypoint queue
     * @param navigationController Existing navigation controller for traversability calculations
     */
    public WaypointNavigationController(Vehicle vehicle, WaypointsManager waypointsManager, NavigationController navigationController) {
        this.vehicle = vehicle;
        this.waypointsManager = waypointsManager;
        this.navigationController = navigationController;
        this.mainHandler = new Handler(Looper.getMainLooper());

        // Initialize with default rule-based controller
        this.waypointController = new RuleBasedWaypointController(ControllerParameters.createDefaultRuleBased());

        Timber.d("WaypointNavigationController initialized with existing NavigationController and %s",
                waypointController.getControllerName());
    }

    /**
     * Set the navigation listener for state updates
     *
     * @param listener The listener to receive navigation updates
     */
    public void setNavigationListener(NavigationListener listener) {
        this.navigationListener = listener;
    }

    /**
     * Set the waypoint controller algorithm
     *
     * @param controller The waypoint controller to use
     */
    public void setWaypointController(WaypointController controller) {
        this.waypointController = controller;
        this.waypointController.reset(); // Reset state when switching controllers
        Timber.i("Switched to waypoint controller: %s", controller.getControllerName());
    }

    /**
     * Get the current waypoint controller
     *
     * @return The current waypoint controller
     */
    public WaypointController getWaypointController() {
        return waypointController;
    }

    /**
     * Start waypoint navigation
     * This will begin navigating to the first waypoint in the queue
     */
    public void startNavigation() {
        if (currentState != NavigationState.IDLE) {
            Timber.w("Navigation already in progress, current state: %s", currentState);
            return;
        }

        if (!waypointsManager.hasNextWaypoint()) {
            Timber.w("No waypoints available for navigation");
            notifyNavigationError("No waypoints available");
            return;
        }

        // Capture the total waypoint count before consuming any
        totalWaypointCount = waypointsManager.getWaypointCount();
        Timber.d("Starting navigation with %d total waypoints", totalWaypointCount);

        // Reset waypoint index and peek at the first waypoint (don't consume until reached)
        currentWaypointIndex = 0;
        currentWaypoint = waypointsManager.peekNextWaypointInLocalCoordinates();
        if (currentWaypoint == null) {
            Timber.e("Failed to peek at first waypoint - local coordinate conversion failed");
            notifyNavigationError("Failed to peek at first waypoint");
            return;
        }

        // Reset controller state and timing
        waypointController.reset();
        lastUpdateTime = System.currentTimeMillis();

        // Start navigation to the first waypoint
        setState(NavigationState.TURNING);
        Timber.i("Started navigation to waypoint %d of %d: %s", currentWaypointIndex + 1, totalWaypointCount, currentWaypoint.toString());
    }

    /**
     * Stop waypoint navigation
     * This will stop the vehicle and reset the navigation state
     */
    public void stopNavigation() {
        setState(NavigationState.IDLE);
        stopVehicle();
        currentWaypoint = null;
        currentWaypointIndex = 0;
        totalWaypointCount = 0;
        Timber.i("Navigation stopped");
    }

    /**
     * Update the current pose from ARCore
     *
     * @param pose The current robot pose
     */
    public void updateCurrentPose(Pose pose) {
        this.currentPose = pose;

        // Debug: Log pose updates occasionally
        if (System.currentTimeMillis() % 3000 < 50) { // Log every ~3 seconds
            float[] translation = new float[3];
            pose.getTranslation(translation, 0);
            Timber.d("WaypointNav pose update: (%.2f, %.2f, %.2f), state: %s",
                    translation[0], translation[1], translation[2], currentState);
        }

        // Process navigation if we're actively navigating
        if (currentState == NavigationState.TURNING || currentState == NavigationState.MOVING) {
            processNavigation();
        }
    }

    /**
     * Update navigation map data for traversability checking
     * This delegates to the NavigationController which handles the actual traversability calculations
     *
     * @param navigabilityData Array of boolean values indicating if each row is navigable
     */
    public void updateNavigationData(boolean[] navigabilityData) {
        // Delegate to the NavigationController for traversability calculations
        navigationController.updateNavigabilityData(navigabilityData);

        // Process navigation if we're actively moving (not turning)
        if (currentState == NavigationState.MOVING) {
            processNavigation();
        }
    }

    /**
     * Get the current navigation state
     *
     * @return Current navigation state
     */
    public NavigationState getCurrentState() {
        return currentState;
    }

    /**
     * Get the current waypoint being navigated to
     *
     * @return Current waypoint JSON object, or null if none
     */
    public JSONObject getCurrentWaypoint() {
        return currentWaypoint;
    }

    /**
     * Check if navigation is active
     *
     * @return true if currently navigating, false otherwise
     */
    public boolean isNavigating() {
        return currentState == NavigationState.TURNING || currentState == NavigationState.MOVING;
    }

    /**
     * Get the underlying NavigationController for direct access to traversability calculations
     *
     * @return The NavigationController instance
     */
    public NavigationController getNavigationController() {
        return navigationController;
    }

    /**
     * Main navigation processing method
     * Called when pose or navigation data is updated
     */
    private void processNavigation() {
        if (currentPose == null || currentWaypoint == null) {
            return;
        }

        try {
            // Get waypoint coordinates
            double waypointX = currentWaypoint.getDouble("x");
            double waypointZ = currentWaypoint.getDouble("z");

            // Calculate distance and angle to waypoint
            float[] currentTranslation = new float[3];
            currentPose.getTranslation(currentTranslation, 0);

            float deltaX = (float) waypointX - currentTranslation[0];
            float deltaZ = (float) waypointZ - currentTranslation[2];
            float distanceToWaypoint = (float) Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

            // Calculate angle to waypoint and heading error using unified NavigationUtils
            float angleToWaypoint = NavigationUtils.calculateAngleToWaypoint(deltaX, deltaZ);

            float[] currentRotation = new float[4];
            currentPose.getRotationQuaternion(currentRotation, 0);
            float currentYaw = NavigationUtils.getYawFromQuaternion(currentRotation);
            float angleDifference = NavigationUtils.calculateHeadingError(currentYaw, angleToWaypoint);

            Timber.d("WaypointNav - Target: (%.2f, %.2f), Current: (%.2f, %.2f), " +
                    "DeltaX: %.2f, DeltaZ: %.2f, Distance: %.2f m, AngleToWaypoint: %.1f°, CurrentYaw: %.1f°, AngleDiff: %.1f°",
                    waypointX, waypointZ, currentTranslation[0], currentTranslation[2],
                    deltaX, deltaZ, distanceToWaypoint, Math.toDegrees(angleToWaypoint),
                    Math.toDegrees(currentYaw), Math.toDegrees(angleDifference));

            // Check if we've reached the current waypoint
            if (distanceToWaypoint < POSITION_THRESHOLD_METERS) {
                onWaypointReached();
                return;
            }

            // Process based on current state
            switch (currentState) {
                case TURNING:
                    processTurning(angleDifference);
                    break;
                case MOVING:
                    processMoving(distanceToWaypoint, angleDifference);
                    break;
                default:
                    break;
            }

        } catch (JSONException e) {
            Timber.e(e, "Error processing waypoint coordinates");
            notifyNavigationError("Error processing waypoint coordinates");
        }
    }

    /**
     * Process turning towards waypoint
     *
     * @param headingError Heading error to waypoint in radians
     */
    private void processTurning(float headingError) {
        // Check if we're aligned with the waypoint using NavigationUtils
        if (NavigationUtils.isAligned(headingError, ROTATION_THRESHOLD_DEGREES)) {
            // Switch to moving state
            setState(NavigationState.MOVING);
            Timber.d("Aligned with waypoint, switching to moving state");
            return;
        }

        // Calculate deltaTime for controller
        long currentTime = System.currentTimeMillis();
        float deltaTime = (currentTime - lastUpdateTime) / 1000.0f;
        lastUpdateTime = currentTime;

        // Calculate angular velocity using the waypoint controller
        float angularVelocity = waypointController.calculateTurningAngularVelocity(headingError, MAX_TURN_SPEED, deltaTime);

        // Send turn command (no linear velocity when turning)
        sendControlCommand(0.0f, angularVelocity);

        float headingErrorDegrees = (float) Math.toDegrees(Math.abs(headingError));
        Timber.d("Turning (%s): heading error = %.1f degrees, angular velocity = %.2f",
                waypointController.getControllerName(), headingErrorDegrees, angularVelocity);
    }

    /**
     * Process moving towards waypoint
     *
     * @param distanceToWaypoint Distance to waypoint in meters
     * @param headingError Heading error to waypoint in radians
     */
    private void processMoving(float distanceToWaypoint, float headingError) {
        // If we're significantly off course, switch back to turning
        if (!NavigationUtils.isAligned(headingError, ROTATION_THRESHOLD_DEGREES * 2)) {
            setState(NavigationState.TURNING);
            Timber.d("Off course, switching back to turning state");
            return;
        }

        // Calculate speed based on navigation data (traversability) and distance to waypoint
        float linearSpeed = calculateLinearSpeed(distanceToWaypoint);

        // Calculate deltaTime for controller
        long currentTime = System.currentTimeMillis();
        float deltaTime = (currentTime - lastUpdateTime) / 1000.0f;
        lastUpdateTime = currentTime;

        // Apply minor course corrections while moving using the waypoint controller
        float angularVelocity = waypointController.calculateCourseCorrection(headingError, 0.2f, 5.0f, deltaTime);

        // Send movement command
        sendControlCommand(linearSpeed, angularVelocity);

        Timber.d("Moving (%s): distance = %.2f m, linear = %.2f, angular = %.2f",
                waypointController.getControllerName(), distanceToWaypoint, linearSpeed, angularVelocity);
    }

    /**
     * Calculate linear speed based on navigation data and distance to waypoint
     *
     * @param distanceToWaypoint Distance to the target waypoint in meters
     * @return Linear speed (0.0 to MAX_MOVE_SPEED)
     */
    private float calculateLinearSpeed(float distanceToWaypoint) {
        // Get the current linear speed from the NavigationController
        // which has already processed the traversability data
        float navigationLinearSpeed = navigationController.getCurrentLinearSpeed();

        // The NavigationController uses its own speed limits, so we need to scale
        // to our waypoint navigation speed limits
        if (navigationLinearSpeed <= 0.0f) {
            return 0.0f; // Stop if NavigationController says stop
        }

        // Scale the NavigationController's speed to our speed range
        // NavigationController uses MIN_LINEAR_SPEED=0.05f and MAX_LINEAR_SPEED=0.25f
        // We want to map this to our MIN_MOVE_SPEED=0.1f and MAX_MOVE_SPEED=0.3f
        float navigationMinSpeed = 0.05f; // NavigationController.MIN_LINEAR_SPEED
        float navigationMaxSpeed = 0.25f; // NavigationController.MAX_LINEAR_SPEED

        // Normalize the navigation speed to 0-1 range
        float normalizedNavigationSpeed = (navigationLinearSpeed - navigationMinSpeed) / (navigationMaxSpeed - navigationMinSpeed);
        normalizedNavigationSpeed = Math.max(0.0f, Math.min(1.0f, normalizedNavigationSpeed));

        // Calculate distance-based speed factor
        // Slow down as we approach the waypoint to avoid overshooting
        float distanceSpeedFactor = calculateDistanceSpeedFactor(distanceToWaypoint);

        // Combine navigation speed (based on obstacles) with distance speed factor
        float combinedSpeedFactor = normalizedNavigationSpeed * distanceSpeedFactor;

        // Scale to our speed range
        float finalSpeed = MIN_MOVE_SPEED + combinedSpeedFactor * (MAX_MOVE_SPEED - MIN_MOVE_SPEED);

        Timber.d("Speed calculation: distance=%.2fm, navSpeed=%.3f, distFactor=%.3f, final=%.3f",
                distanceToWaypoint, normalizedNavigationSpeed, distanceSpeedFactor, finalSpeed);

        return finalSpeed;
    }

    /**
     * Calculate speed factor based on distance to waypoint
     * This implements a smooth deceleration as the robot approaches the target
     *
     * @param distanceToWaypoint Distance to waypoint in meters
     * @return Speed factor (0.0 to 1.0)
     */
    private float calculateDistanceSpeedFactor(float distanceToWaypoint) {
        // Define distance thresholds
        final float FULL_SPEED_DISTANCE = 2.0f;    // Distance at which to use full speed
        final float SLOW_DOWN_DISTANCE = 0.5f;     // Distance at which to start slowing down significantly
        final float MIN_SPEED_FACTOR = 0.3f;       // Minimum speed factor (never go below this)

        if (distanceToWaypoint >= FULL_SPEED_DISTANCE) {
            // Far from waypoint - use full speed
            return 1.0f;
        } else if (distanceToWaypoint <= SLOW_DOWN_DISTANCE) {
            // Very close to waypoint - use minimum speed
            return MIN_SPEED_FACTOR;
        } else {
            // In between - linear interpolation for smooth deceleration
            float ratio = (distanceToWaypoint - SLOW_DOWN_DISTANCE) / (FULL_SPEED_DISTANCE - SLOW_DOWN_DISTANCE);
            return MIN_SPEED_FACTOR + ratio * (1.0f - MIN_SPEED_FACTOR);
        }
    }

    /**
     * Called when a waypoint is reached
     */
    private void onWaypointReached() {
        Timber.i("Waypoint %d reached: %s", currentWaypointIndex + 1, currentWaypoint.toString());

        // Consume the waypoint we just reached
        waypointsManager.getNextWaypoint();

        // Move to the next waypoint index
        currentWaypointIndex++;

        // Check if there are more waypoints available
        if (waypointsManager.hasNextWaypoint()) {
            // Peek at the next waypoint (don't consume until reached)
            currentWaypoint = waypointsManager.peekNextWaypointInLocalCoordinates();
            if (currentWaypoint != null) {
                // Reset controller state for new waypoint
                waypointController.reset();
                lastUpdateTime = System.currentTimeMillis();

                // Start navigating to the next waypoint
                setState(NavigationState.TURNING);
                Timber.i("Moving to next waypoint %d of %d: %s", currentWaypointIndex + 1, totalWaypointCount, currentWaypoint.toString());
            } else {
                // Error peeking at next waypoint
                Timber.e("Failed to peek at next waypoint");
                notifyNavigationError("Failed to peek at next waypoint");
            }
        } else {
            // All waypoints completed
            setState(NavigationState.COMPLETED);
            stopVehicle();
            notifyNavigationCompleted();
            Timber.i("All waypoints completed");
        }
    }

    /**
     * Send control command to vehicle
     *
     * @param linearVelocity Linear velocity (-1.0 to 1.0)
     * @param angularVelocity Angular velocity (-1.0 to 1.0)
     */
    private void sendControlCommand(float linearVelocity, float angularVelocity) {
        // Ensure values are within valid range
        float safeLinear = Math.max(-1.0f, Math.min(1.0f, linearVelocity));
        float safeAngular = Math.max(-1.0f, Math.min(1.0f, angularVelocity));

        // Debug: Log all control commands
        Timber.d("WaypointNav sending control: linear=%.3f, angular=%.3f", safeLinear, safeAngular);

        // Send command on main thread
        mainHandler.post(() -> {
            try {
                vehicle.setControlVelocity(safeLinear, safeAngular);
                vehicle.sendControl();
                Timber.d("WaypointNav control command sent successfully");
            } catch (Exception e) {
                Timber.e(e, "Error sending control command");
            }
        });
    }

    /**
     * Stop the vehicle
     */
    private void stopVehicle() {
        sendControlCommand(0.0f, 0.0f);
    }

    /**
     * Set the navigation state and notify listener
     *
     * @param newState The new navigation state
     */
    private void setState(NavigationState newState) {
        NavigationState oldState = currentState;
        currentState = newState;

        if (oldState != newState) {
            Timber.d("Navigation state changed: %s -> %s", oldState, newState);
            notifyNavigationStateChanged(newState, currentWaypoint);
        }
    }



    /**
     * Notify listener of navigation state change
     */
    private void notifyNavigationStateChanged(NavigationState state, JSONObject waypoint) {
        if (navigationListener != null) {
            mainHandler.post(() -> navigationListener.onNavigationStateChanged(state, waypoint));
        }
    }

    /**
     * Notify listener of navigation completion
     */
    private void notifyNavigationCompleted() {
        if (navigationListener != null) {
            mainHandler.post(() -> navigationListener.onNavigationCompleted());
        }
    }

    /**
     * Notify listener of navigation error
     */
    private void notifyNavigationError(String error) {
        if (navigationListener != null) {
            mainHandler.post(() -> navigationListener.onNavigationError(error));
        }
    }
}
