package com.satinavrobotics.satibot.livekit;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.satinavrobotics.satibot.R;
import com.satinavrobotics.satibot.databinding.FragmentLivekitInfoBinding;
import com.satinavrobotics.satibot.utils.ConnectionUtils;
import com.satinavrobotics.satibot.utils.PermissionUtils;

import timber.log.Timber;

public class LiveKitInfoFragment extends Fragment {

    private FragmentLivekitInfoBinding binding;
    private LiveKitServer liveKitServer;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentLivekitInfoBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        liveKitServer = LiveKitServer.getInstance(requireContext());

        setupUI();
        updateConnectionStatus();

        // Update status every 2 seconds
        view.postDelayed(this::updateConnectionStatus, 2000);
    }

    private void setupUI() {
        binding.connectButton.setOnClickListener(v -> {
            if (!PermissionUtils.hasControllerPermissions(requireActivity())) {
                PermissionUtils.showControllerPermissionsToast(requireActivity());
                return;
            }

            if (liveKitServer.isRoomConnected()) {
                liveKitServer.disconnect();
            } else {
                // Check internet connectivity before attempting to connect
                if (!ConnectionUtils.isInternetAvailable(requireContext())) {
                    Toast.makeText(requireContext(),
                        "No internet connection available. Please check your network connection and try again.",
                        Toast.LENGTH_LONG).show();
                    return;
                }
                liveKitServer.connect();
            }

            // Update status after a short delay
            binding.getRoot().postDelayed(this::updateConnectionStatus, 1000);
        });


    }

    private void updateConnectionStatus() {
        if (binding == null || liveKitServer == null) {
            return;
        }

        try {
            boolean isConnected = liveKitServer.isConnected();
            boolean isRoomConnected = liveKitServer.isRoomConnected();

            // Update connection status
            binding.connectionStatusText.setText(isRoomConnected ? "Connected" : "Disconnected");
            binding.connectionStatusText.setTextColor(
                getResources().getColor(isRoomConnected ? R.color.green : R.color.red, null)
            );

            // Update room state
            String roomState = liveKitServer.getRoomState();
            binding.roomStateText.setText("Room State: " + roomState);

            // Update server URL
            String serverUrl = liveKitServer.getServerUrl();
            binding.serverUrlText.setText("Server: " + (serverUrl != null ? serverUrl : "Not available"));

            // Update participant info
            String participantInfo = liveKitServer.getParticipantInfo();
            binding.participantInfoText.setText("Participant: " + participantInfo);

            // Update connection quality
            String connectionQuality = liveKitServer.getConnectionQuality();
            binding.connectionQualityText.setText("Connection Quality: " + connectionQuality);

            // Update button states
            binding.connectButton.setText(isRoomConnected ? "Disconnect" : "Connect");
            binding.connectButton.setEnabled(true);

        } catch (Exception e) {
            Timber.e(e, "Error updating connection status");
            binding.connectionStatusText.setText("Error");
            binding.connectionStatusText.setTextColor(getResources().getColor(R.color.red, null));
        }

        // Schedule next update
        if (binding != null) {
            binding.getRoot().postDelayed(this::updateConnectionStatus, 2000);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
