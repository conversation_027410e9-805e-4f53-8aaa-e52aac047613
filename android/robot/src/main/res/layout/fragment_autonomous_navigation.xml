<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.satinavrobotics.satibot.robot.DepthNavigationFragment">

    <android.opengl.GLSurfaceView
        android:id="@+id/surfaceView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Navigation Map Overlay -->
    <include
        android:id="@+id/navMapOverlay"
        layout="@layout/nav_map_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Robot Bounds Overlay -->
    <include
        android:id="@+id/robotBoundsOverlay"
        layout="@layout/robot_bounds_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/statusText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="#80000000"
        android:padding="8dp"
        android:text="Initializing..."
        android:textColor="#FFFFFF"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/costValuesText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:background="#80000000"
        android:padding="8dp"
        android:text="Costs: L:-.-- C:-.-- R:-.-"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        android:fontFamily="monospace"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/statusText" />

    <TextView
        android:id="@+id/navigationErrorText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:background="#80000000"
        android:padding="8dp"
        android:text="Target: No waypoint"
        android:textColor="#FFFF00"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/costValuesText" />

    <TextView
        android:id="@+id/velocityText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:background="#CC000000"
        android:padding="8dp"
        android:text="Velocity: 0.0"
        android:textColor="#FFFF00"
        android:textSize="16sp"
        android:textStyle="bold"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/anchorCountText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:background="#CC000000"
        android:padding="8dp"
        android:text="Anchors: 0/0"
        android:textColor="#00FF00"
        android:textSize="14sp"
        android:textStyle="bold"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/velocityText" />

    <androidx.cardview.widget.CardView
        android:id="@+id/controlPanel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardBackgroundColor="#80000000"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/navigationStatusText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Ready for navigation"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp" />

                <Button
                    android:id="@+id/startStopButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Start"
                    android:textSize="14sp"
                    android:backgroundTint="#4CAF50"
                    android:padding="8dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Waypoints:"
                    android:textColor="#FFFFFF"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/waypointsCountText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="0 remaining"
                    android:textColor="#FFFFFF"
                    android:textSize="14sp" />

                <Button
                    android:id="@+id/displayModeButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Display Mode"
                    android:textSize="12sp"
                    android:backgroundTint="#3F51B5"
                    android:padding="8dp" />
            </LinearLayout>

            <TextView
                android:id="@+id/fpsText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="0.0 FPS"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>
