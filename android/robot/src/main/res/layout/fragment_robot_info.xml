<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/robotTypeText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="8dp"
            android:text="@string/robot_type"
            android:textColor="@android:color/black"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/robotTypeInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="8dp"
            android:text="@string/n_a"
            android:textColor="@android:color/black" />

        <CheckBox
            android:id="@+id/refreshToggle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/refresh_toggle"
            android:minWidth="48dp"
            android:padding="8dp"
            tools:ignore="DuplicateSpeakableTextCheck" />

        <CheckBox
            android:id="@+id/usbToggle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/usb_toggle"
            android:padding="8dp" />

    </LinearLayout>

    <pl.droidsonroids.gif.GifImageView
        android:id="@+id/robot_icon"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:contentDescription="@string/robot_icon"
        android:padding="8dp"
        android:src="@drawable/ic_openbot" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/readingsText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="sans-serif-black"
            android:padding="8dp"
            android:text="@string/readings"
            android:textColor="@android:color/black" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/label_battery"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:minHeight="32dp"
            android:padding="4dp"
            android:text="@string/battery"
            android:textColor="@android:color/black" />

        <TextView
            android:id="@+id/voltage_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:minHeight="32dp"
            android:padding="4dp"
            android:text="@string/voltage"
            android:textColor="@android:color/black" />

        <TextView
            android:id="@+id/label_speed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:gravity="end"
            android:minHeight="32dp"
            android:padding="4dp"
            android:text="@string/speed"
            android:textColor="@android:color/black" />

        <TextView
            android:id="@+id/speed_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:minHeight="32dp"
            android:padding="4dp"
            android:text="@string/rpm"
            android:textColor="@android:color/black" />

        <TextView
            android:id="@+id/label_sonar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:gravity="end"
            android:minHeight="32dp"
            android:padding="4dp"
            android:text="@string/sonar"
            android:textColor="@android:color/black" />

        <TextView
            android:id="@+id/sonar_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:minHeight="32dp"
            android:padding="4dp"
            android:text="@string/distance"
            android:textColor="@android:color/black" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/label_wheel_encoder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:minHeight="32dp"
            android:padding="4dp"
            android:text="@string/wheel_encoder_angular_velocity"
            android:textColor="@android:color/black" />

        <TextView
            android:id="@+id/wheel_encoder_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:minHeight="32dp"
            android:padding="4dp"
            android:text="@string/angular_velocity_format"
            android:textColor="@android:color/black" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/label_imu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:minHeight="32dp"
            android:padding="4dp"
            android:text="@string/imu_angular_velocity"
            android:textColor="@android:color/black" />

        <TextView
            android:id="@+id/imu_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:minHeight="32dp"
            android:padding="4dp"
            android:text="@string/angular_velocity_format"
            android:textColor="@android:color/black" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/label_fused"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:minHeight="32dp"
            android:padding="4dp"
            android:text="@string/fused_angular_velocity"
            android:textColor="@android:color/black" />

        <TextView
            android:id="@+id/fused_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:minHeight="32dp"
            android:padding="4dp"
            android:text="@string/angular_velocity_format"
            android:textColor="@android:color/black" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/label_pwm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:minHeight="32dp"
            android:padding="4dp"
            android:text="@string/current_pwm"
            android:textColor="@android:color/black" />

        <TextView
            android:id="@+id/pwm_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:minHeight="32dp"
            android:padding="4dp"
            android:text="@string/pwm_format"
            android:textColor="@android:color/black" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/label_wheel_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:minHeight="32dp"
            android:padding="4dp"
            android:text="@string/wheel_count"
            android:textColor="@android:color/black" />

        <TextView
            android:id="@+id/wheel_count_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:minHeight="32dp"
            android:padding="4dp"
            android:text="@string/wheel_count_format"
            android:textColor="@android:color/black" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/sendCommandsText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="sans-serif-black"
            android:padding="8dp"
            android:text="@string/send_commands"
            android:textColor="@android:color/black" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/motors_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:gravity="end"
            android:minHeight="48dp"
            android:padding="4dp"
            android:text="@string/motors"
            android:textColor="@android:color/black" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/motors_forward_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:backgroundTint="@color/satiBotRed"
            android:minHeight="48dp"
            android:padding="16dp"
            android:text="@string/forward"
            android:textAppearance="@style/TextAppearance.AppCompat.Body1"
            android:textColor="#FFFFFF"
            tools:ignore="TextContrastCheck" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/motors_backward_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:backgroundTint="@color/satiBotRed"
            android:minHeight="48dp"
            android:padding="16dp"
            android:text="@string/backward"
            android:textAllCaps="false"
            android:textAppearance="@style/TextAppearance.AppCompat.Body1"
            android:textColor="#FFFFFF"
            tools:ignore="TextContrastCheck" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/motors_stop_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:backgroundTint="@color/satiBotRed"
            android:minHeight="48dp"
            android:padding="16dp"
            android:text="@string/stop"
            android:textAllCaps="false"
            android:textAppearance="@style/TextAppearance.AppCompat.Body1"
            android:textColor="#FFFFFF"
            tools:ignore="TextContrastCheck" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/leds_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:gravity="end"
            android:minHeight="48dp"
            android:padding="4dp"
            android:text="@string/lights"
            android:textColor="@android:color/black" />

        <com.google.android.material.slider.RangeSlider
            android:id="@+id/lights_slider"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="32dp"
            android:contentDescription="@string/lights_slider"
            android:padding="4dp"
            android:valueFrom="0.0"
            android:valueTo="100.0"
            app:thumbColor="@color/satiBotRed"
            app:trackColorActive="@color/satiBotRed" />


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/speed_multiplier_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:gravity="end"
            android:minHeight="48dp"
            android:padding="4dp"
            android:text="@string/speed_multiplier"
            android:textColor="@android:color/black" />

        <com.google.android.material.slider.Slider
            android:id="@+id/speed_multiplier_slider"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="32dp"
            android:contentDescription="@string/speed_multiplier_slider"
            android:padding="4dp"
            android:valueFrom="50.0"
            android:valueTo="255.0"
            android:stepSize="1.0"
            app:thumbColor="@color/satiBotRed"
            app:trackColorActive="@color/satiBotRed" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/angular_multiplier_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:gravity="end"
            android:minHeight="48dp"
            android:padding="4dp"
            android:text="@string/angular_multiplier"
            android:textColor="@android:color/black" />

        <com.google.android.material.slider.Slider
            android:id="@+id/angular_multiplier_slider"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="32dp"
            android:contentDescription="@string/angular_multiplier_slider"
            android:padding="4dp"
            android:valueFrom="50.0"
            android:valueTo="255.0"
            android:stepSize="1.0"
            app:thumbColor="@color/satiBotRed"
            app:trackColorActive="@color/satiBotRed" />

    </LinearLayout>
</LinearLayout>