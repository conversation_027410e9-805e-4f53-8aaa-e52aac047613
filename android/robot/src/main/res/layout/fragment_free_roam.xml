<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/constraintLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.satinavrobotics.satibot.robot.RemoteControlFragment">



    <!-- GLSurfaceView for ARCore -->
    <android.opengl.GLSurfaceView
        android:id="@+id/surfaceView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <CheckBox
        android:id="@+id/usbToggle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:button="@drawable/usb_toggle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="16dp" />

    <CheckBox
        android:id="@+id/bleToggle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:button="@drawable/ble_toggle"
        app:layout_constraintStart_toEndOf="@+id/usbToggle"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="16dp" />

    <ImageView
        android:id="@+id/steering"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/ic_car_steering_wheel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="16dp" />

    <TextView
        android:id="@+id/driveGear"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/rectangle"
        android:gravity="center"
        android:text="P"
        android:textColor="@android:color/black"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/steering"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="16dp" />

    <ImageView
        android:id="@+id/indicatorLeft"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_margin="@dimen/feed_padding"
        android:src="@drawable/circle"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/indicator"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/indicatorRight"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_margin="@dimen/feed_padding"
        android:layout_marginTop="88dp"
        android:layout_marginEnd="32dp"
        android:src="@drawable/circle"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/indicator"
        tools:visibility="visible" />



    <ImageView
        android:id="@+id/recordingIndicator"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_margin="@dimen/feed_padding"
        android:layout_marginTop="88dp"
        android:src="@drawable/circle"
        android:visibility="invisible"
        app:layout_constraintEnd_toStartOf="@+id/indicatorRight"
        app:layout_constraintStart_toEndOf="@+id/indicatorLeft"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/recording"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/anchor_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="#80000000"
        android:padding="8dp"
        android:text="Anchors: 0/0"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>