<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/nav_map_overlay_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="visible">

    <!-- Container for navigation rows -->
    <LinearLayout
        android:id="@+id/nav_map_rows_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="0dp"
        android:background="@android:color/transparent"
        android:orientation="vertical"
        android:visibility="visible">
        <!-- Navigation rows will be added here programmatically -->
    </LinearLayout>

    <!-- Border for the navigation map (optional) -->
    <View
        android:id="@+id/nav_map_border"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="0dp"
        android:background="@android:color/transparent"
        android:visibility="gone" />

</FrameLayout>
