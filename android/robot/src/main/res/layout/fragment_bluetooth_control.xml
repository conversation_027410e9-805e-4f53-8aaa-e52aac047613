<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:keepScreenOn="true">

    <!-- Top Controls Bar -->
    <LinearLayout
        android:id="@+id/topControlsBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:gravity="center_vertical"
        app:layout_constraintTop_toTopOf="parent">

        <!-- Status Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Empty placeholder to maintain layout structure -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
            </LinearLayout>

            <!-- Hidden TextViews to maintain callbacks without UI elements -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:visibility="gone">

                <TextView
                    android:id="@+id/speedInfo"
                    android:layout_width="0dp"
                    android:layout_height="0dp"/>

                <TextView
                    android:id="@+id/headingAdjustmentInfo"
                    android:layout_width="0dp"
                    android:layout_height="0dp"/>

                <TextView
                    android:id="@+id/currentHeadingInfo"
                    android:layout_width="0dp"
                    android:layout_height="0dp"/>

                <TextView
                    android:id="@+id/targetHeadingInfo"
                    android:layout_width="0dp"
                    android:layout_height="0dp"/>

                <TextView
                    android:id="@+id/normalizedLinearVelocityInfo"
                    android:layout_width="0dp"
                    android:layout_height="0dp"/>

                <TextView
                    android:id="@+id/targetAngularVelocityInfo"
                    android:layout_width="0dp"
                    android:layout_height="0dp"/>
            </LinearLayout>
        </LinearLayout>

        <!-- Connection Toggles -->
        <LinearLayout
            android:id="@+id/connectionToggles"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ToggleButton
                android:id="@+id/hapticToggle"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="@drawable/haptic_toggle"
                android:textOff=""
                android:textOn=""/>

            <ToggleButton
                android:id="@+id/usbToggle"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginStart="8dp"
                android:background="@drawable/usb_toggle"
                android:textOff=""
                android:textOn=""/>

            <ToggleButton
                android:id="@+id/bleToggle"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginStart="8dp"
                android:background="@drawable/ble_toggle"
                android:textOff=""
                android:textOn=""/>
        </LinearLayout>
    </LinearLayout>

    <!-- Main Content Area -->
    <FrameLayout
        android:id="@+id/mainContentArea"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/topControlsBar"
        app:layout_constraintBottom_toTopOf="@id/bottomControlsBar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- On-Screen Controls Container -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/sliderControlsLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone">
            <!-- This layout is hidden and no longer used -->
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Tilt Controls -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/tiltControlsLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone">
            <!-- This layout is hidden and no longer used -->
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Game-like Controls Layout -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/wheelControlsLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible">

            <!-- Left Side Joystick (Acceleration) - Now on right side -->
            <FrameLayout
                android:id="@+id/leftJoystickContainer"
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:background="@drawable/joystick_background_red"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginEnd="32dp"
                android:layout_marginBottom="32dp">

                <ImageView
                    android:id="@+id/accelerationArrowUp"
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:src="@drawable/ic_arrow_up"
                    android:layout_gravity="center_horizontal|top"
                    android:contentDescription="Forward" />

                <ImageView
                    android:id="@+id/accelerationArrowDown"
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:src="@drawable/ic_arrow_down"
                    android:layout_gravity="center_horizontal|bottom"
                    android:contentDescription="Backward" />

                <View
                    android:id="@+id/accelerationJoystickThumb"
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:background="@drawable/joystick_thumb_red"
                    android:layout_gravity="center" />

                <SeekBar
                    android:id="@+id/forwardSeekBar"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:visibility="gone"
                    android:rotation="270"
                    android:progress="50"
                    android:max="100" />
            </FrameLayout>

            <!-- Right Side Joystick (Steering) - Now on left side -->
            <FrameLayout
                android:id="@+id/rightJoystickContainer"
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:background="@drawable/joystick_background_red"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginStart="32dp"
                android:layout_marginBottom="32dp">

                <ImageView
                    android:id="@+id/steeringArrowLeft"
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:src="@drawable/ic_arrow_left"
                    android:layout_gravity="start|center_vertical"
                    android:contentDescription="Left" />

                <ImageView
                    android:id="@+id/steeringArrowRight"
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:src="@drawable/ic_arrow_right"
                    android:layout_gravity="end|center_vertical"
                    android:contentDescription="Right" />

                <View
                    android:id="@+id/steeringJoystickThumb"
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:background="@drawable/joystick_thumb_red"
                    android:layout_gravity="center" />

                <SeekBar
                    android:id="@+id/rightSeekBar"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:visibility="gone"
                    android:progress="50"
                    android:max="100" />
            </FrameLayout>

            <!-- Connection Status (Hidden) -->
            <TextView
                android:id="@+id/connectionStatus"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <!-- Emergency Stop Button (Top Left) -->
            <Button
                android:id="@+id/centerStopButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="STOP"
                android:textColor="#FFFFFF"
                android:backgroundTint="#FF0000"
                android:textStyle="bold"
                android:gravity="center"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_margin="0dp" />

            <!-- Control Info (Center of Screen) -->
            <TextView
                android:id="@+id/controlInfo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Control: 0,0"
                android:textColor="#FFFFFF"
                android:textSize="24sp"
                android:textStyle="bold"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>

    <!-- Bottom Controls Bar (Empty) -->
    <LinearLayout
        android:id="@+id/bottomControlsBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        app:layout_constraintBottom_toBottomOf="parent">
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
