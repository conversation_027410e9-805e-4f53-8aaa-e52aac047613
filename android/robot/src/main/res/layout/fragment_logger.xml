<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.satinavrobotics.satibot.logging.LoggerFragment">

    <!-- GLSurfaceView for ARCore-->
    <android.opengl.GLSurfaceView
        android:id="@+id/surfaceView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="fill" />

    <!-- PreviewView for Camera sources with hardware acceleration -->
    <androidx.camera.view.PreviewView
        android:id="@+id/cameraPreviewView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="fill"
        android:visibility="gone" />

    <!-- SurfaceView for External Camera sources -->
    <SurfaceView
        android:id="@+id/cameraSurfaceView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="fill"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/recordingIndicator"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_margin="@dimen/feed_padding"
        android:layout_marginTop="88dp"
        android:src="@drawable/circle"
        android:visibility="invisible"
        app:layout_constraintEnd_toStartOf="@+id/indicatorRight"
        app:layout_constraintStart_toEndOf="@+id/indicatorLeft"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/recording"
        tools:visibility="visible" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/loggerBottomSheet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bottom_sheet_bg"
        app:behavior_hideable="false"
        app:behavior_peekHeight="48dp"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior"
        tools:behavior_peekHeight="480dp">

        <View
            android:id="@+id/view"
            android:layout_width="48dp"
            android:layout_height="5dp"
            android:layout_marginTop="16dp"
            android:alpha="0.5"
            android:background="@drawable/button_item_background_black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <CheckBox
            android:id="@+id/usbToggle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:button="@drawable/usb_toggle"
            app:layout_constraintBottom_toBottomOf="@+id/view"
            app:layout_constraintEnd_toStartOf="@+id/bleToggle"
            app:layout_constraintTop_toTopOf="@+id/view" />
        <CheckBox
            android:id="@+id/bleToggle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:button="@drawable/ble_toggle"
            app:layout_constraintBottom_toBottomOf="@+id/view"
            app:layout_constraintEnd_toStartOf="@+id/gamepad_indicator"
            app:layout_constraintTop_toTopOf="@+id/view" />

        <CheckBox
            android:id="@+id/gamepad_indicator"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_margin="16dp"
            android:background="@android:color/transparent"
            android:button="@drawable/gamepad_toggle"
            android:clickable="false"
            android:enabled="false"
            app:layout_constraintBottom_toBottomOf="@+id/view"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/view" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/logger_switch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="8dp"
            android:layout_margin="8dp"
            android:text="Log Data"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="@+id/view"
            app:layout_constraintTop_toTopOf="@+id/view" />

        <TextView
            android:id="@+id/imageSourceText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="24dp"
            android:padding="8dp"
            android:text="Image Source"
            android:textColor="@android:color/black"
            app:layout_constraintBottom_toTopOf="@+id/analyseText"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view" />

        <Spinner
            android:id="@+id/image_source_spinner"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginStart="100dp"
            android:layout_marginEnd="16dp"
            android:entries="@array/image_sources"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="@+id/imageSourceText"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/imageSourceText" />

        <TextView
            android:id="@+id/analyseText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:padding="8dp"
            android:text="Resolution"
            android:textColor="@android:color/black"
            app:layout_constraintBottom_toTopOf="@+id/fpsText"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imageSourceText" />

        <Spinner
            android:id="@+id/resolution_spinner"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginStart="100dp"
            android:layout_marginEnd="16dp"
            android:entries="@array/resolution_values"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="@+id/analyseText"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/analyseText" />

        <TextView
            android:id="@+id/fpsText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:padding="8dp"
            android:text="FPS"
            android:textColor="@android:color/black"
            app:layout_constraintBottom_toTopOf="@+id/server"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/analyseText" />

        <Spinner
            android:id="@+id/fps_spinner"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginStart="100dp"
            android:layout_marginEnd="16dp"
            android:entries="@array/fps_values"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="@+id/fpsText"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/fpsText" />

        <TextView
            android:id="@+id/server"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:padding="8dp"
            android:text="@string/save_as"
            android:textColor="@android:color/black"
            app:layout_constraintBottom_toTopOf="@+id/linearLayout2"
            app:layout_constraintStart_toStartOf="parent" />

        <Spinner
            android:id="@+id/save_as"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginEnd="16dp"
            android:entries="@array/save_data"
            android:prompt="@string/save_option"
            android:textColor="@android:color/black"
            app:layout_constraintBottom_toBottomOf="@+id/server"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/resolution_spinner"
            app:layout_constraintTop_toTopOf="@+id/server" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/linearLayout2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">



        </androidx.constraintlayout.widget.ConstraintLayout>




    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>