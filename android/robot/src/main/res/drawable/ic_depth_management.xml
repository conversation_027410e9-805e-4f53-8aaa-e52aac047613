<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="24"
    android:viewportHeight="24"
    android:tint="?attr/colorControlNormal">
    
    <!-- Depth layers representing different depth levels -->
    <!-- Closest layer (brightest) -->
    <path
        android:fillColor="@android:color/white"
        android:pathData="M4,4h16v3H4z"
        android:fillAlpha="1.0"/>
    
    <!-- Middle layer -->
    <path
        android:fillColor="@android:color/white"
        android:pathData="M6,8h12v3H6z"
        android:fillAlpha="0.7"/>
    
    <!-- Far layer -->
    <path
        android:fillColor="@android:color/white"
        android:pathData="M8,12h8v3H8z"
        android:fillAlpha="0.4"/>
    
    <!-- Farthest layer (dimmest) -->
    <path
        android:fillColor="@android:color/white"
        android:pathData="M10,16h4v3h-4z"
        android:fillAlpha="0.2"/>
    
    <!-- Depth measurement lines -->
    <path
        android:fillColor="@android:color/white"
        android:pathData="M2,6L2,8L3,8L3,6z"/>
    <path
        android:fillColor="@android:color/white"
        android:pathData="M2,10L2,12L3,12L3,10z"/>
    <path
        android:fillColor="@android:color/white"
        android:pathData="M2,14L2,16L3,16L3,14z"/>
    <path
        android:fillColor="@android:color/white"
        android:pathData="M2,18L2,20L3,20L3,18z"/>
        
    <!-- Settings/management icon in corner -->
    <path
        android:fillColor="@android:color/white"
        android:pathData="M19.5,20.5m-1.5,0a1.5,1.5 0,1 1,3 0a1.5,1.5 0,1 1,-3 0"/>
    <path
        android:fillColor="@android:color/white"
        android:pathData="M21,19h-1v-1h1v1z"/>
    <path
        android:fillColor="@android:color/white"
        android:pathData="M21,22h-1v-1h1v1z"/>
</vector>
