<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="32dp"
    android:height="32dp"
    android:viewportWidth="24"
    android:viewportHeight="24"
    android:tint="?attr/colorControlNormal">
    <!-- Robot body -->
    <path
        android:fillColor="@android:color/white"
        android:pathData="M6,4h12c1.1,0 2,0.9 2,2v12c0,1.1 -0.9,2 -2,2H6c-1.1,0 -2,-0.9 -2,-2V6c0,-1.1 0.9,-2 2,-2z"/>
    <!-- Robot eyes -->
    <path
        android:fillColor="@android:color/black"
        android:pathData="M9,8m-1,0a1,1 0,1 1,2 0a1,1 0,1 1,-2 0"/>
    <path
        android:fillColor="@android:color/black"
        android:pathData="M15,8m-1,0a1,1 0,1 1,2 0a1,1 0,1 1,-2 0"/>
    <!-- Robot mouth -->
    <path
        android:fillColor="@android:color/black"
        android:pathData="M8,14h8c0.55,0 1,-0.45 1,-1s-0.45,-1 -1,-1H8c-0.55,0 -1,0.45 -1,1s0.45,1 1,1z"/>
    <!-- Robot antenna -->
    <path
        android:fillColor="@android:color/white"
        android:pathData="M12,2v2h-1v-2h1z"/>
    <path
        android:fillColor="@android:color/white"
        android:pathData="M12,1m-0.5,0a0.5,0.5 0,1 1,1 0a0.5,0.5 0,1 1,-1 0"/>
</vector>
