<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="24"
    android:viewportHeight="24"
    android:tint="?attr/colorControlNormal">
    
    <!-- Central robot/sensor point -->
    <path
        android:fillColor="@android:color/white"
        android:pathData="M12,10m-1.5,0a1.5,1.5 0,1 1,3 0a1.5,1.5 0,1 1,-3 0"/>
    
    <!-- Depth sensing arcs (radar-like) -->
    <path
        android:fillColor="@android:color/white"
        android:pathData="M12,6C8.69,6 6,8.69 6,12h2c0,-2.21 1.79,-4 4,-4s4,1.79 4,4h2C18,8.69 15.31,6 12,6z"/>
    
    <path
        android:fillColor="@android:color/white"
        android:pathData="M12,2C6.48,2 2,6.48 2,12h2c0,-4.41 3.59,-8 8,-8s8,3.59 8,8h2C22,6.48 17.52,2 12,2z"/>
    
    <!-- Navigation path/arrow -->
    <path
        android:fillColor="@android:color/white"
        android:pathData="M12,14l-3,3h2v5h2v-5h2L12,14z"/>
</vector>
