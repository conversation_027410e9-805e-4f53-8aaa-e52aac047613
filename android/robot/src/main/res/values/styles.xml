<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar.Bridge">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowBackground">@color/satiBotRed</item>
    </style>

    <style name="AppThemeToolbar" parent="Theme.MaterialComponents.Light.NoActionBar.Bridge">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="elevation">0dp</item>
    </style>

    <style name="SpinnerTheme" parent="android:Widget.Spinner">
        <item name="android:background">@drawable/spinner_bg</item>
    </style>


    <style name="ShapeAppearanceOverlay.App.CornerSize10Percent" parent="">
        <item name="cornerSize">8dp</item>
    </style>
    <style name="ShapeAppearanceOverlay.App.CornerSize50Percent" parent="">
        <item name="cornerSize">50%</item>
    </style>

    <style name="FilePickerTheme" parent="NNF_BaseTheme">
        <!-- Set these to match your theme -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>

        <!-- Setting a divider is entirely optional -->
        <item name="nnf_list_item_divider">?android:attr/listDivider</item>

        <!-- Need to set this also to style create folder dialog -->
        <item name="alertDialogTheme">@style/FilePickerAlertDialogTheme</item>

        <!-- If you want to set a specific toolbar theme, do it here -->
        <!-- <item name="nnf_toolbarTheme">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item> -->
    </style>

    <style name="FilePickerAlertDialogTheme" parent="Theme.AppCompat.Dialog.Alert">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="FullScreenDialog" parent="Theme.MaterialComponents.Light.NoActionBar.Bridge">
        <item name="colorPrimaryDark">@color/lightGray</item>
    </style>

</resources>
