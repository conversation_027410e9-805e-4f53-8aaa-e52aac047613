<?xml version="1.0" encoding="utf-8"?>
<menu
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <item
        android:id="@+id/liveKitInfoFragment"
        android:icon="@drawable/ic_livekit_disconnected"
        android:title="@string/livekit"
        app:showAsAction="ifRoom"/>
    <item
        android:id="@+id/usbFragment"
        android:icon="@drawable/ic_usb_menu_item"
        android:title="@string/usb"
        app:showAsAction="ifRoom"/>
    <item
        android:id="@+id/bluetoothFragment"
        android:icon="@drawable/ic_bluetooth"
        android:title="@string/bluetooth"
        app:showAsAction="ifRoom" />
    <item
        android:id="@+id/settingsFragment"
        android:icon="@drawable/ic_settings"
        android:title="@string/settings"
        app:showAsAction="ifRoom"/>
</menu>