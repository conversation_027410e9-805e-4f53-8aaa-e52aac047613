<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" package="com.satinavrobotics.satibot">

    <!-- Basic permissions -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <!-- Android 13+ permissions -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />

    <!-- Google Play Services permissions -->
    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />


    <uses-permission android:name="android.permission.BLUETOOTH" android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />

    <!-- RTSP Video streaming -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <!-- Haptic feedback -->
    <uses-permission android:name="android.permission.VIBRATE" />


    <uses-feature android:name="android.hardware.camera" android:required="true"/>
    <uses-feature android:name="android.hardware.camera.autofocus" android:required="true"/>
    <uses-feature android:name="android.hardware.camera.front" android:required="true" />
    <uses-feature android:name="android.hardware.usb.host" android:required="true"/>
    <uses-feature android:name="android.hardware.sensor.accelerometer" android:required="false"/>
    <uses-feature android:name="android.hardware.sensor.gyroscope" android:required="false"/>
    <uses-feature android:name="android.hardware.sensor.light" android:required="false"/>
    <uses-feature android:name="android.hardware.bluetooth_le" android:required="true"/>

    <!-- ARCore and Geospatial API -->
    <uses-feature android:name="com.google.ar.core.depth" android:required="false"/>
    <uses-feature android:glEsVersion="0x00020000" android:required="true" />
    <uses-feature android:name="android.hardware.location.gps" android:required="true"/>

    <application
        android:name=".SatiBotApplication"
        android:allowBackup="false"
        android:icon="@mipmap/ic_satibot"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_satibot_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        android:hardwareAccelerated="true"
        android:largeHeap="true">

        <!-- ARCore and Geospatial API -->
        <!-- Google Play Services and Firebase configuration -->
        <meta-data android:name="com.google.android.gms.version"
                   android:value="@integer/google_play_services_version" />

        <!-- ARCore configuration -->
        <meta-data android:name="com.google.ar.core"
                   android:value="required" />
        <meta-data android:name="com.google.ar.core.session_resumeRequiresUserPrompt"
                   android:value="false" />
        <activity android:name=".main.MainActivity"
            android:launchMode="singleInstance"
            android:configChanges="screenSize|orientation"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
                <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED" />
            </intent-filter>

            <meta-data
                android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"
                android:resource="@xml/device_filter" />
        </activity>
        <activity
            android:name=".original.DefaultActivity"
            android:label="@string/activity_name"
            android:noHistory="false">
            <!--
                        <intent-filter>
                            <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED" />
                        </intent-filter>x

                        <meta-data
                            android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"
                            android:resource="@xml/device_filter" />
            -->

        </activity>

        <activity
            android:name=".modelManagement.BackHandlingFilePickerActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/FilePickerTheme">
            <intent-filter>
                <action android:name="android.intent.action.GET_CONTENT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <service
            android:name=".logging.LocationService"
            android:enabled="true"
            android:launchMode="singleTask" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>


        <!-- Google Vision API configuration -->
        <meta-data
            android:name="com.google.android.gms.vision.DEPENDENCIES"
            android:value="barcode" />

    </application>

</manifest>