plugins {
    id 'io.github.reactivecircus.app-versioning' version "1.1.2"
    id 'com.google.gms.google-services'
    id 'com.android.application'
}

appVersioning {
    /**
     * Git root directory used for fetching git tags.
     * Use this to explicitly set the git root directory when the root Gradle project is not the git root directory.
     */
    gitRootDirectory.set(rootProject.file("../"))
    // if the .git directory is in the root Gradle project's parent directory.
}

apply plugin: 'com.android.application'
apply plugin: 'de.undercouch.download'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'com.google.gms.google-services'


android {
    compileSdkVersion 34
    //buildToolsVersion '34.0.1'
    defaultConfig {
        applicationId "com.satinavrobotics.satibot"
        minSdkVersion 33
        targetSdkVersion 34
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }

        // Increase Java heap size to handle large models
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [
                    "room.incremental": "true"
                ]
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }


    buildFeatures {
        viewBinding true
    }

    compileOptions {
        coreLibraryDesugaringEnabled true

        sourceCompatibility = '17'
        targetCompatibility = '17'
    }
    androidResources {
        noCompress 'tflite', 'mp3'
    }
    lint {
        abortOnError false
        checkReleaseBuilds false
    }
    namespace 'com.satinavrobotics.satibot'
    packagingOptions {
        resources.excludes.add("META-INF/*")
    }
}

// Download default models
project.ext.ASSET_DIR = projectDir.toString() + '/src/main/assets'
apply from: 'download.gradle'
apply plugin: 'kotlin-android'

// Define the path to the comlib lib directory
def comlibDir = file("${project.rootDir}/comlib/libs")

dependencies {

    // WiFi direct + WebRTC
    implementation project(':comlib')

    // Include AAR files from comlib/lib
    implementation fileTree(dir: comlibDir, include: ['*.aar'])

    implementation 'com.google.android.material:material:1.4.0'

    // LiteRT dependencies for Google Play services
    implementation 'com.google.android.gms:play-services-tflite-java:16.3.0'
    implementation 'com.google.android.gms:play-services-tflite-support:16.1.0'
    implementation 'com.google.android.gms:play-services-tflite-gpu:16.3.0'

    // Exclude the standalone TensorFlow Lite dependencies to avoid conflicts
    configurations.all {
        exclude group: 'org.tensorflow', module: 'tensorflow-lite'
        exclude group: 'org.tensorflow', module: 'tensorflow-lite-gpu'
    }

    // Google Play Services core dependencies
    implementation 'com.google.android.gms:play-services-base:18.2.0'
    implementation 'com.google.android.gms:play-services-basement:18.2.0'
    implementation 'com.google.android.gms:play-services-tasks:18.0.2'
    implementation 'com.google.android.gms:play-services-auth:20.5.0'
    implementation 'com.google.android.gms:play-services-identity:18.0.1'

    // Other Google Play Services
    implementation "com.google.android.gms:play-services-location:$location_version"
    implementation "com.google.android.gms:play-services-nearby:$nearby_version"
    implementation 'androidx.coordinatorlayout:coordinatorlayout:1.1.0'
    implementation 'org.zeroturnaround:zt-zip:1.14'
    implementation 'com.loopj.android:android-async-http:1.4.9'
    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.0.0'

    // RTSP server
    implementation 'com.github.pedroSG94:RTSP-Server:1.0.8'
    implementation 'com.github.pedroSG94.rtmp-rtsp-stream-client-java:rtplibrary:2.0.2'

    // RxJava
    implementation 'io.reactivex.rxjava3:rxjava:3.0.0'
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.0'
    implementation 'org.jetbrains:annotations:16.0.1'

    implementation 'androidx.appcompat:appcompat:1.3.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    implementation 'androidx.recyclerview:recyclerview:1.2.0'
    implementation 'androidx.lifecycle:lifecycle-livedata:2.3.1'
    implementation 'androidx.lifecycle:lifecycle-viewmodel:2.3.1'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.preference:preference:1.1.1'
    implementation 'com.google.firebase:firebase-auth'
    implementation 'com.google.firebase:firebase-firestore:24.5.0'
    implementation 'com.google.firebase:firebase-storage:20.1.0'

    def nav_version = "2.3.5"
    // Java language implementation
    implementation "androidx.navigation:navigation-fragment:$nav_version"
    implementation "androidx.navigation:navigation-ui:$nav_version"

    implementation 'com.github.bumptech.glide:glide:4.11.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.11.0'

    def camerax_version = "1.0.0"
    // CameraX core library using camera2 implementation
    implementation "androidx.camera:camera-camera2:$camerax_version"
    // CameraX Lifecycle Library
    implementation "androidx.camera:camera-lifecycle:$camerax_version"
    // CameraX View class
    implementation "androidx.camera:camera-view:1.0.0-alpha24"
    implementation 'com.github.anastr:speedviewlib:1.6.1'

    implementation 'com.jakewharton.timber:timber:4.7.1'
    implementation 'androidx.fragment:fragment:1.3.4'

    testImplementation 'junit:junit:4.13.2'
    testImplementation 'androidx.test.ext:junit:1.2.1'
    testImplementation 'androidx.test:core:1.6.1'
    testImplementation "org.robolectric:robolectric:4.12.1"

    // Core library
    androidTestImplementation 'androidx.test:core:1.5.0'

    // AndroidJUnitRunner and JUnit Rules
    androidTestImplementation 'androidx.test:runner:1.3.0'
    androidTestImplementation 'androidx.test:rules:1.3.0'

    // Assertions
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.ext:truth:1.3.0'
    androidTestImplementation 'com.google.truth:truth:1.0'

    // Espresso dependencies
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
    androidTestImplementation 'androidx.test.espresso:espresso-contrib:3.3.0'
    androidTestImplementation 'androidx.test.espresso:espresso-intents:3.3.0'
    androidTestImplementation 'androidx.test.espresso:espresso-accessibility:3.3.0'
    androidTestImplementation 'androidx.test.espresso:espresso-web:3.3.0'
    androidTestImplementation 'androidx.test.espresso.idling:idling-concurrent:3.3.0'

    // The following Espresso dependency can be either "implementation"
    // or "androidTestImplementation", depending on whether you want the
    // dependency to appear on your APK's compile classpath or the test APK
    // classpath.
    androidTestImplementation 'androidx.test.espresso:espresso-idling-resource:3.3.0'

    implementation platform('com.google.firebase:firebase-bom:31.5.0')
    // Declare the dependencies for the Crashlytics and Analytics libraries
    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.nononsenseapps:filepicker:4.2.1'

    // arcore - updated to latest version for new Cloud Anchors API
    implementation 'com.google.ar:core:1.48.0'
    implementation 'de.javagl:obj:0.2.1'

    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.1.5'
    implementation 'com.koushikdutta.ion:ion:3.1.0'
    implementation 'com.google.code.gson:gson:2.8.6'

    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.25'

    // BLE
    implementation 'com.github.Ficat:EasyBle:v2.0.2'
    implementation 'com.github.Ficat:EasyPermissions:v2.1.0'

    // Socket Server
    implementation 'com.squareup.okhttp3:okhttp:4.9.1'

    //Scan QR
    implementation 'com.google.android.gms:play-services-vision:20.1.3'
    implementation('com.google.api-client:google-api-client-android:1.31.0') {
        exclude group: 'org.apache.httpcomponents'
        exclude module: 'guava-jdk5'
    }
    implementation('com.google.apis:google-api-services-drive:v3-rev136-1.25.0') {
        exclude group: 'org.apache.httpcomponents'
        exclude module: 'guava-jdk5'
    }

    implementation "io.livekit:livekit-android:2.+"
    implementation "io.livekit:livekit-android-compose-components:1.+"
    implementation "androidx.security:security-crypto:1.1.0-alpha06"

    // ONNX Runtime dependencies
    implementation 'com.microsoft.onnxruntime:onnxruntime-android:1.16.3'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:1.8.22"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"

}

// NOTE: added these to cover livekit dependencies and resolve conflicts
configurations.all {
    resolutionStrategy {
        force "androidx.emoji2:emoji2:1.3.0"
        force "androidx.core:core-ktx:1.10.0"
        force "androidx.core:core:1.10.0"
        force "androidx.annotation:annotation-experimental:1.3.0"

        // Force consistent Google Play Services versions
        force "com.google.android.gms:play-services-base:18.2.0"
        force "com.google.android.gms:play-services-basement:18.2.0"
        force "com.google.android.gms:play-services-tasks:18.0.2"
    }
}
